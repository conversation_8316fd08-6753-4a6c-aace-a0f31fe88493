-- GymKod Pro - Optimize Edilmiş Index'ler
-- Proje analizi sonucu belirlenen kritik index'ler
-- 10.000+ kullanıcı için optimize edilmiş

-- =====================================================
-- MEMBERS TABLOSU - EN KRİTİK TABLO
-- =====================================================

-- 1. CompanyID + IsActive (Tüm member sorgularında kullanılıyor)
CREATE INDEX IX_Members_CompanyID_IsActive ON Members(CompanyID, IsActive) 
INCLUDE (MemberID, Name, PhoneNumber, Gender, ScanNumber, Balance);

-- 2. Name arama için (LIKE sorguları)
CREATE INDEX IX_Members_Name_CompanyID ON Members(Name, CompanyID) 
WHERE IsActive = 1;

-- 3. PhoneNumber arama için (LIKE sorguları + QR kod)
CREATE INDEX IX_Members_PhoneNumber_CompanyID ON Members(PhoneNumber, CompanyID) 
WHERE IsActive = 1;

-- 4. ScanNumber için (QR kod tarama - mobile app)
CREATE INDEX IX_Members_ScanNumber_CompanyID ON Members(ScanNumber, CompanyID) 
WHERE IsActive = 1;

-- 5. Gender filtreleme için
CREATE INDEX IX_Members_Gender_CompanyID_IsActive ON Members(Gender, CompanyID, IsActive);

-- 6. UserID ilişkisi için (mobile app authentication)
CREATE INDEX IX_Members_UserID_CompanyID ON Members(UserID, CompanyID) 
WHERE IsActive = 1;

-- =====================================================
-- MEMBERSHIPS TABLOSU - İKİNCİ EN KRİTİK
-- =====================================================

-- 7. MemberID + IsActive (Member-Membership JOIN'i)
CREATE INDEX IX_Memberships_MemberID_IsActive_CompanyID ON Memberships(MemberID, IsActive, CompanyID) 
INCLUDE (MembershipID, MembershipTypeID, StartDate, EndDate, IsFrozen);

-- 8. EndDate + IsActive (Aktif üyelik kontrolü)
CREATE INDEX IX_Memberships_EndDate_IsActive_CompanyID ON Memberships(EndDate, IsActive, CompanyID) 
WHERE EndDate > GETDATE();

-- 9. StartDate + EndDate + IsActive (Tarih aralığı sorguları)
CREATE INDEX IX_Memberships_StartDate_EndDate_IsActive ON Memberships(StartDate, EndDate, IsActive, CompanyID) 
WHERE IsFrozen = 0;

-- 10. MembershipTypeID + CompanyID (Paket filtreleme)
CREATE INDEX IX_Memberships_MembershipTypeID_CompanyID_IsActive ON Memberships(MembershipTypeID, CompanyID, IsActive) 
INCLUDE (MemberID, StartDate, EndDate);

-- =====================================================
-- ENTRYEXITHISTORIES TABLOSU - YOĞUN KULLANIM
-- =====================================================

-- 11. MembershipID + EntryDate (Giriş-çıkış sorguları)
CREATE INDEX IX_EntryExitHistories_MembershipID_EntryDate_CompanyID ON EntryExitHistories(MembershipID, EntryDate, CompanyID) 
WHERE IsActive = 1;

-- 12. EntryDate + IsActive (Günlük giriş listesi)
CREATE INDEX IX_EntryExitHistories_EntryDate_IsActive_CompanyID ON EntryExitHistories(EntryDate, IsActive, CompanyID) 
INCLUDE (MembershipID, ExitDate);

-- 13. CompanyID + EntryDate (Şirket bazlı giriş sorguları)
CREATE INDEX IX_EntryExitHistories_CompanyID_EntryDate_IsActive ON EntryExitHistories(CompanyID, EntryDate, IsActive) 
WHERE EntryDate IS NOT NULL;

-- =====================================================
-- PAYMENTS TABLOSU - ÖDEME GEÇMİŞİ
-- =====================================================

-- 14. MemberShipID + IsActive (Payment-Membership JOIN)
CREATE INDEX IX_Payments_MemberShipID_IsActive_CompanyID ON Payments(MemberShipID, IsActive, CompanyID) 
INCLUDE (PaymentID, PaymentDate, PaymentAmount, PaymentMethod, PaymentStatus);

-- 15. PaymentDate + IsActive (Tarih filtreleme)
CREATE INDEX IX_Payments_PaymentDate_IsActive_CompanyID ON Payments(PaymentDate, IsActive, CompanyID) 
INCLUDE (PaymentAmount, PaymentMethod);

-- 16. PaymentStatus + CompanyID (Durum filtreleme)
CREATE INDEX IX_Payments_PaymentStatus_CompanyID_IsActive ON Payments(PaymentStatus, CompanyID, IsActive) 
WHERE PaymentStatus IS NOT NULL;

-- 17. CompanyID + PaymentDate (Şirket bazlı ödeme raporları)
CREATE INDEX IX_Payments_CompanyID_PaymentDate_IsActive ON Payments(CompanyID, PaymentDate, IsActive) 
INCLUDE (PaymentAmount, PaymentMethod);

-- =====================================================
-- TRANSACTIONS TABLOSU - ÜYE İŞLEMLERİ
-- =====================================================

-- 18. MemberID + TransactionDate (Üye işlem geçmişi)
CREATE INDEX IX_Transactions_MemberID_TransactionDate_IsActive ON Transactions(MemberID, TransactionDate, IsActive) 
INCLUDE (TransactionID, Amount, TransactionType, IsPaid);

-- 19. TransactionDate + IsPaid (Ödeme durumu sorguları)
CREATE INDEX IX_Transactions_TransactionDate_IsPaid_CompanyID ON Transactions(TransactionDate, IsPaid, CompanyID) 
WHERE IsActive = 1;

-- 20. CompanyID + IsPaid (Şirket bazlı ödenmemiş işlemler)
CREATE INDEX IX_Transactions_CompanyID_IsPaid_IsActive ON Transactions(CompanyID, IsPaid, IsActive) 
INCLUDE (MemberID, Amount, TransactionDate);

-- =====================================================
-- USERS TABLOSU - AUTHENTICATION
-- =====================================================

-- 21. Email + IsActive (Login sorguları)
CREATE INDEX IX_Users_Email_IsActive ON Users(Email, IsActive) 
INCLUDE (UserID, PasswordHash, PasswordSalt, RequirePasswordChange);

-- 22. UserID + IsActive (User lookup)
CREATE INDEX IX_Users_UserID_IsActive ON Users(UserID, IsActive);

-- =====================================================
-- USEROPERATIONCLAIMS TABLOSU - YETKİLENDİRME
-- =====================================================

-- 23. UserID + IsActive (Kullanıcı yetkileri)
CREATE INDEX IX_UserOperationClaims_UserID_IsActive ON UserOperationClaims(UserID, IsActive) 
INCLUDE (OperationClaimId);

-- 24. OperationClaimID + IsActive (Rol bazlı sorgular)
CREATE INDEX IX_UserOperationClaims_OperationClaimID_IsActive ON UserOperationClaims(OperationClaimId, IsActive) 
INCLUDE (UserID);

-- =====================================================
-- MEMBERSHIPTYPE TABLOSU - PAKET YÖNETİMİ
-- =====================================================

-- 25. CompanyID + IsActive (Şirket paketleri)
CREATE INDEX IX_MembershipTypes_CompanyID_IsActive ON MembershipTypes(CompanyID, IsActive) 
INCLUDE (MembershipTypeID, TypeName, Branch, Price, Day);

-- 26. Branch + CompanyID (Şube filtreleme)
CREATE INDEX IX_MembershipTypes_Branch_CompanyID_IsActive ON MembershipTypes(Branch, CompanyID, IsActive);

-- =====================================================
-- REMAININGDEBTS TABLOSU - BORÇ TAKİBİ
-- =====================================================

-- 27. PaymentID + IsActive (Borç-ödeme ilişkisi)
CREATE INDEX IX_RemainingDebts_PaymentID_IsActive ON RemainingDebts(PaymentID, IsActive) 
INCLUDE (RemainingDebtID, RemainingAmount, LastUpdateDate);

-- 28. LastUpdateDate + IsActive (Borç güncelleme tarihi)
CREATE INDEX IX_RemainingDebts_LastUpdateDate_IsActive ON RemainingDebts(LastUpdateDate, IsActive) 
WHERE RemainingAmount > 0;

-- =====================================================
-- COMPANYUSERS TABLOSU - ŞİRKET KULLANICILARI
-- =====================================================

-- 29. Email + IsActive (Company user login)
CREATE INDEX IX_CompanyUsers_Email_IsActive ON CompanyUsers(Email, IsActive) 
INCLUDE (CompanyUserID, PhoneNumber, CityID);

-- 30. PhoneNumber + IsActive (Telefon arama)
CREATE INDEX IX_CompanyUsers_PhoneNumber_IsActive ON CompanyUsers(PhoneNumber, IsActive);

-- 31. CityID + IsActive (Şehir filtreleme)
CREATE INDEX IX_CompanyUsers_CityID_IsActive ON CompanyUsers(CityID, IsActive);

-- =====================================================
-- MEMBERWORKOUTPROGRAMS TABLOSU - ANTRENMAN PROGRAMLARI
-- =====================================================

-- 32. MemberID + IsActive (Üye antrenman programları - mobile app)
CREATE INDEX IX_MemberWorkoutPrograms_MemberID_IsActive ON MemberWorkoutPrograms(MemberID, IsActive) 
INCLUDE (MemberWorkoutProgramId, WorkoutProgramTemplateId, AssignedDate);

-- 33. WorkoutProgramTemplateId + IsActive (Program şablonu)
CREATE INDEX IX_MemberWorkoutPrograms_TemplateId_IsActive ON MemberWorkoutPrograms(WorkoutProgramTemplateId, IsActive);

-- =====================================================
-- EXPENSES TABLOSU - GİDER YÖNETİMİ
-- =====================================================

-- 34. CompanyID + ExpenseDate (Şirket giderleri)
CREATE INDEX IX_Expenses_CompanyID_ExpenseDate_IsActive ON Expenses(CompanyID, ExpenseDate, IsActive) 
INCLUDE (ExpenseID, Amount, ExpenseType, Description);

-- 35. ExpenseDate + IsActive (Tarih filtreleme)
CREATE INDEX IX_Expenses_ExpenseDate_IsActive ON Expenses(ExpenseDate, IsActive) 
INCLUDE (Amount, ExpenseType);

PRINT 'Tüm optimize edilmiş indexler başarıyla oluşturuldu!';
