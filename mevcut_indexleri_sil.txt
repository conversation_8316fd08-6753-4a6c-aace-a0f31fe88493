-- Mevcut indexleri silme script'i
-- Bu script'i çalıştırmadan önce veritabanının yedeğini alın!

-- Mevcut indexleri sil (SQL Server syntax ile)
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Email' AND object_id = OBJECT_ID('Users'))
    DROP INDEX IX_Users_Email ON Users;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_IsActive_Email' AND object_id = OBJECT_ID('Users'))
    DROP INDEX IX_Users_IsActive_Email ON Users;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Members_ScanNumber' AND object_id = OBJECT_ID('Members'))
    DROP INDEX IX_Members_ScanNumber ON Members;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Members_PhoneNumber' AND object_id = OBJECT_ID('Members'))
    DROP INDEX IX_Members_PhoneNumber ON Members;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Members_IsActive_Name' AND object_id = OBJECT_ID('Members'))
    DROP INDEX IX_Members_IsActive_Name ON Members;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Memberships_MemberID_IsActive' AND object_id = OBJECT_ID('Memberships'))
    DROP INDEX IX_Memberships_MemberID_IsActive ON Memberships;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Memberships_EndDate_IsActive' AND object_id = OBJECT_ID('Memberships'))
    DROP INDEX IX_Memberships_EndDate_IsActive ON Memberships;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Memberships_StartDate_EndDate_IsActive' AND object_id = OBJECT_ID('Memberships'))
    DROP INDEX IX_Memberships_StartDate_EndDate_IsActive ON Memberships;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Payments_MembershipID_IsActive' AND object_id = OBJECT_ID('Payments'))
    DROP INDEX IX_Payments_MembershipID_IsActive ON Payments;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Payments_PaymentDate_IsActive' AND object_id = OBJECT_ID('Payments'))
    DROP INDEX IX_Payments_PaymentDate_IsActive ON Payments;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Payments_PaymentStatus' AND object_id = OBJECT_ID('Payments'))
    DROP INDEX IX_Payments_PaymentStatus ON Payments;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_EntryExitHistories_MembershipID_EntryDate' AND object_id = OBJECT_ID('EntryExitHistories'))
    DROP INDEX IX_EntryExitHistories_MembershipID_EntryDate ON EntryExitHistories;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_EntryExitHistories_EntryDate_IsActive' AND object_id = OBJECT_ID('EntryExitHistories'))
    DROP INDEX IX_EntryExitHistories_EntryDate_IsActive ON EntryExitHistories;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Transactions_MemberID_TransactionDate' AND object_id = OBJECT_ID('Transactions'))
    DROP INDEX IX_Transactions_MemberID_TransactionDate ON Transactions;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Transactions_TransactionDate_IsPaid' AND object_id = OBJECT_ID('Transactions'))
    DROP INDEX IX_Transactions_TransactionDate_IsPaid ON Transactions;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompanyUsers_Email' AND object_id = OBJECT_ID('CompanyUsers'))
    DROP INDEX IX_CompanyUsers_Email ON CompanyUsers;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompanyUsers_PhoneNumber' AND object_id = OBJECT_ID('CompanyUsers'))
    DROP INDEX IX_CompanyUsers_PhoneNumber ON CompanyUsers;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompanyUsers_IsActive_CityID' AND object_id = OBJECT_ID('CompanyUsers'))
    DROP INDEX IX_CompanyUsers_IsActive_CityID ON CompanyUsers;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RemainingDebts_PaymentID_IsActive' AND object_id = OBJECT_ID('RemainingDebts'))
    DROP INDEX IX_RemainingDebts_PaymentID_IsActive ON RemainingDebts;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RemainingDebts_LastUpdateDate_IsActive' AND object_id = OBJECT_ID('RemainingDebts'))
    DROP INDEX IX_RemainingDebts_LastUpdateDate_IsActive ON RemainingDebts;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserOperationClaims_UserID_IsActive' AND object_id = OBJECT_ID('UserOperationClaims'))
    DROP INDEX IX_UserOperationClaims_UserID_IsActive ON UserOperationClaims;

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserOperationClaims_OperationClaimID_IsActive' AND object_id = OBJECT_ID('UserOperationClaims'))
    DROP INDEX IX_UserOperationClaims_OperationClaimID_IsActive ON UserOperationClaims;

PRINT 'Mevcut indexler başarıyla silindi.';
