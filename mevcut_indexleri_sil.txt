-- Mevcut indexleri silme script'i
-- Bu script'i çalıştırmadan önce veritabanının yedeğini alın!

-- Mevcut indexleri sil
DROP INDEX IF EXISTS IX_Users_Email;
DROP INDEX IF EXISTS IX_Users_IsActive_Email;
DROP INDEX IF EXISTS IX_Members_ScanNumber;
DROP INDEX IF EXISTS IX_Members_PhoneNumber;
DROP INDEX IF EXISTS IX_Members_IsActive_Name;
DROP INDEX IF EXISTS IX_Memberships_MemberID_IsActive;
DROP INDEX IF EXISTS IX_Memberships_EndDate_IsActive;
DROP INDEX IF EXISTS IX_Memberships_StartDate_EndDate_IsActive;
DROP INDEX IF EXISTS IX_Payments_MembershipID_IsActive;
DROP INDEX IF EXISTS IX_Payments_PaymentDate_IsActive;
DROP INDEX IF EXISTS IX_Payments_PaymentStatus;
DROP INDEX IF EXISTS IX_EntryExitHistories_MembershipID_EntryDate;
DROP INDEX IF EXISTS IX_EntryExitHistories_EntryDate_IsActive;
DROP INDEX IF EXISTS IX_Transactions_MemberID_TransactionDate;
DROP INDEX IF EXISTS IX_Transactions_TransactionDate_IsPaid;
DROP INDEX IF EXISTS IX_CompanyUsers_Email;
DROP INDEX IF EXISTS IX_CompanyUsers_PhoneNumber;
DROP INDEX IF EXISTS IX_CompanyUsers_IsActive_CityID;
DROP INDEX IF EXISTS IX_RemainingDebts_PaymentID_IsActive;
DROP INDEX IF EXISTS IX_RemainingDebts_LastUpdateDate_IsActive;
DROP INDEX IF EXISTS IX_UserOperationClaims_UserID_IsActive;
DROP INDEX IF EXISTS IX_UserOperationClaims_OperationClaimID_IsActive;

PRINT 'Mevcut indexler silindi.';
